{"name": "@gd/firestore-rest", "version": "0.0.1", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "edge-light": "./dist/index.js", "browser": "./dist/index.js", "import": "./dist/index.js", "default": "./dist/index.js"}, "./types": {"types": "./dist/types.d.ts", "node": "./dist/types.js", "edge-light": "./dist/types.js", "browser": "./dist/types.js", "import": "./dist/types.js", "default": "./dist/types.js"}}, "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "rslib build && node ./script/encode.mjs", "check": "biome check --write", "dev": "clear && rslib build --watch", "format": "biome format --write", "type-check": "tsc --project tsconfig.check.json", "clean": "rm -rf dist"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@rslib/core": "^0.9.2", "@types/node": "^22.15.30", "javascript-obfuscator": "^4.1.1", "typescript": "^5.8.3"}, "private": true, "dependencies": {"ofetch": "^1.4.1", "zod": "^3.25.56"}}