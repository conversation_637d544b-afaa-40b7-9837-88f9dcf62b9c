import { ofetch } from "ofetch";
import type {
  FirestoreConfig,
  FirestoreDocument,
  FirestoreListResponse,
  CreateDocumentRequest,
  UpdateDocumentRequest,
  QueryOptions,
  FirestoreStructuredQuery,
  FirestoreQueryResponse
} from "./types";
import {
  validateCollectionName,
  validateDocumentId,
  handleFirestoreError,
  toFirestoreValue
} from "./utils";

/**
 * Simple Firestore REST API Client
 * Provides CRUD operations with simple Bearer token authentication
 */
export class SimpleFirestoreClient {
  private config: FirestoreConfig;
  private baseURL: string;
  private client: typeof ofetch;

  constructor(config: FirestoreConfig) {
    this.config = config;
    this.baseURL = `https://firestore.googleapis.com/v1/projects/${config.projectId}/databases/(default)/documents`;

    // Create ofetch client with static headers
    this.client = ofetch.create({
      baseURL: this.baseURL,
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": `Bearer ${config.token}`,
        "X-Goog-Api-Key": config.apiKey,
      },
      onResponseError: ({ response }) => {
        throw new Error(handleFirestoreError(response._data));
      }
    });
  }

  /**
   * Create a new document
   */
  public async createDocument(
    collection: string, 
    data: Record<string, any>, 
    documentId?: string
  ): Promise<FirestoreDocument> {
    validateCollectionName(collection);
    
    if (documentId) {
      validateDocumentId(documentId);
    }

    const url = documentId 
      ? `/${collection}?documentId=${documentId}`
      : `/${collection}`;

    const request: CreateDocumentRequest = {
      fields: {}
    };

    // Convert data to Firestore format
    for (const [key, value] of Object.entries(data)) {
      request.fields[key] = toFirestoreValue(value);
    }

    return await this.client(url, {
      method: "POST",
      body: request
    });
  }

  /**
   * Get a document by ID
   */
  public async getDocument(collection: string, documentId: string): Promise<FirestoreDocument> {
    validateCollectionName(collection);
    validateDocumentId(documentId);

    return await this.client(`/${collection}/${documentId}`);
  }

  /**
   * Update a document
   */
  public async updateDocument(
    collection: string, 
    documentId: string, 
    data: Record<string, any>,
    merge: boolean = false
  ): Promise<FirestoreDocument> {
    validateCollectionName(collection);
    validateDocumentId(documentId);

    const request: UpdateDocumentRequest = {
      fields: {}
    };

    // Convert data to Firestore format
    for (const [key, value] of Object.entries(data)) {
      request.fields[key] = toFirestoreValue(value);
    }

    // Add update mask for partial updates
    if (!merge) {
      request.updateMask = {
        fieldPaths: Object.keys(data)
      };
    }

    const url = `/${collection}/${documentId}`;
    const queryParams = request.updateMask 
      ? `?updateMask.fieldPaths=${request.updateMask.fieldPaths.join("&updateMask.fieldPaths=")}`
      : "";

    return await this.client(`${url}${queryParams}`, {
      method: "PATCH",
      body: { fields: request.fields }
    });
  }

  /**
   * Delete a document
   */
  public async deleteDocument(collection: string, documentId: string): Promise<void> {
    validateCollectionName(collection);
    validateDocumentId(documentId);

    await this.client(`/${collection}/${documentId}`, {
      method: "DELETE"
    });
  }

  /**
   * List documents in a collection
   */
  public async listDocuments(
    collection: string, 
    pageSize?: number, 
    pageToken?: string
  ): Promise<FirestoreListResponse> {
    validateCollectionName(collection);

    const params = new URLSearchParams();
    if (pageSize) params.append("pageSize", pageSize.toString());
    if (pageToken) params.append("pageToken", pageToken);

    const url = `/${collection}${params.toString() ? `?${params.toString()}` : ""}`;
    
    return await this.client(url);
  }

  /**
   * Build structured query for complex queries
   */
  private buildStructuredQuery(collection: string, options: QueryOptions): FirestoreStructuredQuery {
    const query: FirestoreStructuredQuery = {
      from: [{ collectionId: collection }]
    };

    // Add where filters
    if (options.where && options.where.length > 0) {
      query.where = {
        compositeFilter: {
          op: "AND",
          filters: options.where.map(filter => ({
            fieldFilter: {
              field: { fieldPath: filter.field },
              op: filter.op,
              value: toFirestoreValue(filter.value)
            }
          }))
        }
      };
    }

    // Add order by
    if (options.orderBy && options.orderBy.length > 0) {
      query.orderBy = options.orderBy.map(order => ({
        field: { fieldPath: order.field },
        direction: order.direction
      }));
    }

    // Add limit
    if (options.limit) {
      query.limit = options.limit;
    }

    // Add offset
    if (options.offset) {
      query.offset = options.offset;
    }

    // Add startAt cursor
    if (options.startAt) {
      query.startAt = {
        values: options.startAt.map(toFirestoreValue),
        before: false
      };
    }

    // Add endAt cursor
    if (options.endAt) {
      query.endAt = {
        values: options.endAt.map(toFirestoreValue),
        before: true
      };
    }

    return query;
  }

  /**
   * Run a structured query
   */
  public async runQuery(collection: string, options: QueryOptions = {}): Promise<FirestoreDocument[]> {
    validateCollectionName(collection);

    const structuredQuery = this.buildStructuredQuery(collection, options);

    const response = await this.client<FirestoreQueryResponse[]>(":runQuery", {
      method: "POST",
      body: { structuredQuery }
    });

    // Extract documents from query response
    if (Array.isArray(response)) {
      return response
        .filter((item): item is FirestoreQueryResponse & { document: FirestoreDocument } =>
          item.document !== undefined
        )
        .map(item => item.document);
    }

    return [];
  }
}
