import type { SimpleFirestoreClient } from "./simple-client";
import type { 
  DocumentData, 
  FirestoreDocument, 
  QueryOptions, 
  WhereFilter, 
  OrderBy 
} from "./types";
import { 
  fromFirestoreFields, 
  extractDocumentId, 
  generateDocumentId 
} from "./utils";

/**
 * High-level operations wrapper for SimpleFirestoreClient
 * Provides user-friendly methods with automatic data conversion
 */
export class SimpleFirestoreOperations {
  constructor(private client: SimpleFirestoreClient) {}

  /**
   * Add a document with auto-generated ID
   */
  public async add(collection: string, data: DocumentData): Promise<{ id: string; data: DocumentData }> {
    const documentId = generateDocumentId();
    const doc = await this.client.createDocument(collection, data, documentId);
    
    return {
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    };
  }

  /**
   * Set a document with specific ID (create or overwrite)
   */
  public async set(collection: string, id: string, data: DocumentData): Promise<{ id: string; data: DocumentData }> {
    const doc = await this.client.createDocument(collection, data, id);
    
    return {
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    };
  }

  /**
   * Get a document by ID
   */
  public async get(collection: string, id: string): Promise<{ id: string; data: DocumentData } | null> {
    try {
      const doc = await this.client.getDocument(collection, id);
      return {
        id: extractDocumentId(doc.name),
        data: fromFirestoreFields(doc.fields)
      };
    } catch (error) {
      // Return null if document doesn't exist
      if (error instanceof Error && error.message.includes("NOT_FOUND")) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Update a document
   */
  public async update(collection: string, id: string, data: DocumentData): Promise<{ id: string; data: DocumentData }> {
    const doc = await this.client.updateDocument(collection, id, data);
    
    return {
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    };
  }

  /**
   * Delete a document
   */
  public async delete(collection: string, id: string): Promise<void> {
    await this.client.deleteDocument(collection, id);
  }

  /**
   * Get all documents in a collection
   */
  public async getAll(collection: string): Promise<Array<{ id: string; data: DocumentData }>> {
    const response = await this.client.listDocuments(collection);
    
    if (!response.documents) {
      return [];
    }

    return response.documents.map(doc => ({
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    }));
  }

  /**
   * Query documents with where filter
   */
  public async where(
    collection: string, 
    field: string, 
    op: WhereFilter["op"], 
    value: any
  ): Promise<Array<{ id: string; data: DocumentData }>> {
    const options: QueryOptions = {
      where: [{ field, op, value }]
    };

    const docs = await this.client.runQuery(collection, options);
    
    return docs.map(doc => ({
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    }));
  }

  /**
   * Query documents with ordering
   */
  public async orderBy(
    collection: string, 
    field: string, 
    direction: "asc" | "desc" = "asc"
  ): Promise<Array<{ id: string; data: DocumentData }>> {
    const options: QueryOptions = {
      orderBy: [{ field, direction: direction === "asc" ? "ASCENDING" : "DESCENDING" }]
    };

    const docs = await this.client.runQuery(collection, options);
    
    return docs.map(doc => ({
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    }));
  }

  /**
   * Limit query results
   */
  public async limit(collection: string, count: number): Promise<Array<{ id: string; data: DocumentData }>> {
    const options: QueryOptions = {
      limit: count
    };

    const docs = await this.client.runQuery(collection, options);
    
    return docs.map(doc => ({
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    }));
  }

  /**
   * Complex query with multiple options
   */
  public async query(collection: string, options: QueryOptions): Promise<Array<{ id: string; data: DocumentData }>> {
    const docs = await this.client.runQuery(collection, options);
    
    return docs.map(doc => ({
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    }));
  }

  /**
   * Search documents by field (simple text search)
   */
  public async search(
    collection: string, 
    field: string, 
    term: string
  ): Promise<Array<{ id: string; data: DocumentData }>> {
    // Simple search using EQUAL operator
    // For more advanced search, you would need to implement full-text search
    return await this.where(collection, field, "EQUAL", term);
  }

  /**
   * Paginate through documents
   */
  public async paginate(
    collection: string,
    pageSize: number,
    pageToken?: string
  ): Promise<{
    documents: Array<{ id: string; data: DocumentData }>;
    nextPageToken?: string;
  }> {
    const response = await this.client.listDocuments(collection, pageSize, pageToken);

    const documents = response.documents ? response.documents.map(doc => ({
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    })) : [];

    const result: {
      documents: Array<{ id: string; data: DocumentData }>;
      nextPageToken?: string;
    } = {
      documents,
    };

    if (response.nextPageToken) {
      result.nextPageToken = response.nextPageToken;
    }

    return result;
  }

  /**
   * Count documents in a collection (with optional filters)
   */
  public async count(collection: string, filters?: WhereFilter[]): Promise<number> {
    const options: QueryOptions = filters ? { where: filters } : {};
    const docs = await this.client.runQuery(collection, options);
    return docs.length;
  }

  /**
   * Check if a document exists
   */
  public async exists(collection: string, id: string): Promise<boolean> {
    const doc = await this.get(collection, id);
    return doc !== null;
  }
}
