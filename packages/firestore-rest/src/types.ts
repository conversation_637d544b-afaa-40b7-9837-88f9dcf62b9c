import { z } from "zod";

// Firestore Value Types
export type FirestoreValue =
  | { stringValue: string }
  | { integerValue: string }
  | { doubleValue: number }
  | { booleanValue: boolean }
  | { timestampValue: string }
  | { nullValue: null }
  | { arrayValue: { values: FirestoreValue[] } }
  | { mapValue: { fields: Record<string, FirestoreValue> } }
  | { referenceValue: string };

// Firestore Document Structure
export type FirestoreDocument = {
  name: string;
  fields: Record<string, FirestoreValue>;
  createTime: string;
  updateTime: string;
};

// Query Types
export type WhereFilter = {
  field: string;
  op: "EQUAL" | "NOT_EQUAL" | "LESS_THAN" | "LESS_THAN_OR_EQUAL" | "GREATER_THAN" | "GREATER_THAN_OR_EQUAL" | "ARRAY_CONTAINS" | "ARRAY_CONTAINS_ANY" | "IN" | "NOT_IN";
  value: any;
};

export type OrderBy = {
  field: string;
  direction: "ASCENDING" | "DESCENDING";
};

export type QueryOptions = {
  where?: WhereFilter[];
  orderBy?: OrderBy[];
  limit?: number;
  offset?: number;
  startAt?: any[];
  endAt?: any[];
};

// API Response Types
export type FirestoreListResponse = {
  documents?: FirestoreDocument[];
  nextPageToken?: string;
};

export type FirestoreError = {
  error: {
    code: number;
    message: string;
    status: string;
  };
};

// Simplified Configuration Types
export type FirestoreConfig = {
  projectId: string;
  apiKey: string;
  token: string;
};

// Structured Query Types
export interface FirestoreStructuredQuery {
  from: Array<{ collectionId: string }>;
  where?: {
    compositeFilter?: {
      op: string;
      filters: Array<{
        fieldFilter: {
          field: { fieldPath: string };
          op: string;
          value: FirestoreValue;
        };
      }>;
    };
  };
  orderBy?: Array<{
    field: { fieldPath: string };
    direction: "ASCENDING" | "DESCENDING";
  }>;
  limit?: number;
  offset?: number;
  startAt?: {
    values: FirestoreValue[];
    before: boolean;
  };
  endAt?: {
    values: FirestoreValue[];
    before: boolean;
  };
}

export interface FirestoreQueryResponse {
  document?: FirestoreDocument;
  readTime?: string;
  skippedResults?: number;
}



// Utility Types for easier usage
export type DocumentData = Record<string, any>;

export type CreateDocumentRequest = {
  fields: Record<string, FirestoreValue>;
};

export type UpdateDocumentRequest = {
  fields: Record<string, FirestoreValue>;
  updateMask?: {
    fieldPaths: string[];
  };
};

// Validation Schemas
export const firestoreConfigSchema = z.object({
  projectId: z.string().min(1, "Project ID is required"),
  apiKey: z.string().min(1, "API Key is required"),
  token: z.string().min(1, "Authentication token is required"),
});

export const whereFilterSchema = z.object({
  field: z.string().min(1, "Field name is required"),
  op: z.enum(["EQUAL", "NOT_EQUAL", "LESS_THAN", "LESS_THAN_OR_EQUAL", "GREATER_THAN", "GREATER_THAN_OR_EQUAL", "ARRAY_CONTAINS", "ARRAY_CONTAINS_ANY", "IN", "NOT_IN"]),
  value: z.any(),
});

export const queryOptionsSchema = z.object({
  where: z.array(whereFilterSchema).optional(),
  orderBy: z.array(z.object({
    field: z.string(),
    direction: z.enum(["ASCENDING", "DESCENDING"]),
  })).optional(),
  limit: z.number().positive().optional(),
  offset: z.number().min(0).optional(),
  startAt: z.array(z.any()).optional(),
  endAt: z.array(z.any()).optional(),
});

// Type Guards
export const isFirestoreError = (response: any): response is FirestoreError => {
  return response && typeof response === 'object' && 'error' in response;
};


