// Main exports for the simplified Firestore REST API library

// Core classes
export { SimpleFirestoreClient } from "./simple-client";
export { SimpleFirestoreOperations } from "./simple-operations";

// Import types and classes for internal use
import type {
  FirestoreConfig,
  DocumentData,
  QueryOptions,
  WhereFilter,
} from "./types";
import { SimpleFirestoreClient } from "./simple-client";
import { SimpleFirestoreOperations } from "./simple-operations";

// Types
export type {
  FirestoreConfig,
  FirestoreDocument,
  FirestoreValue,
  DocumentData,
  QueryOptions,
  WhereFilter,
  OrderBy,
} from "./types";

// Utilities
export {
  toFirestoreValue,
  fromFirestoreValue,
  toFirestoreFields,
  fromFirestoreFields,
  extractDocumentId,
  buildDocumentPath,
  validateCollectionName,
  validateDocumentId,
  generateDocumentId,
  handleFirestoreError,
} from "./utils";

// Validation schemas
export {
  firestoreConfigSchema,
  whereFilterSchema,
  queryOptionsSchema,
  isFirestoreError,
} from "./types";

/**
 * Factory function to create a simple Firestore instance
 * Requires projectId, apiKey, and authentication token
 */
export const createFirestore = (config: FirestoreConfig) => {
  const client = new SimpleFirestoreClient(config);
  const operations = new SimpleFirestoreOperations(client);

  return {
    client,
    operations,
    // Convenience methods
    collection: (name: string) => ({
      add: (data: DocumentData) => operations.add(name, data),
      set: (id: string, data: DocumentData) => operations.set(name, id, data),
      get: (id: string) => operations.get(name, id),
      update: (id: string, data: DocumentData) => operations.update(name, id, data),
      delete: (id: string) => operations.delete(name, id),
      getAll: () => operations.getAll(name),
      where: (field: string, op: WhereFilter["op"], value: unknown) =>
        operations.where(name, field, op, value),
      orderBy: (field: string, direction: "asc" | "desc" = "asc") =>
        operations.orderBy(name, field, direction),
      limit: (count: number) => operations.limit(name, count),
      query: (options: QueryOptions) => operations.query(name, options),
      search: (field: string, term: string) => operations.search(name, field, term),
      paginate: (pageSize: number, pageToken?: string) =>
        operations.paginate(name, pageSize, pageToken),
      count: (filters?: WhereFilter[]) => operations.count(name, filters),
      exists: (id: string) => operations.exists(name, id),
    }),
  };
};
/**
 * Default export for convenience
 */
export default createFirestore;
